"""
统一杀号算法模块
整合所有杀号策略，提供统一的接口
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

from .utils import parse_numbers


class UnifiedKillAlgorithms:
    """统一杀号算法类 - 增强版多样性杀号"""
    
    def __init__(self):
        """初始化杀号算法"""
        self.red_range = list(range(1, 36))  # 红球范围1-35
        self.blue_range = list(range(1, 13))  # 蓝球范围1-12
        
        # 杀号历史记录 - 用于避免重复
        self.kill_history = {
            'red': [],  # 存储最近10期的红球杀号
            'blue': []  # 存储最近10期的蓝球杀号
        }
        
        # 多样性增强参数
        self.diversity_factor = 0.7  # 多样性权重
        self.time_seed_factor = 1000  # 时间种子因子
        self.history_window = 10  # 历史窗口大小
        
        # 策略权重 - 动态调整
        self.strategy_weights = {
            'frequency_inverse': 0.25,  # 反频率策略
            'pattern_break': 0.20,      # 模式打破策略
            'zone_balance': 0.15,       # 区间平衡策略
            'gap_analysis': 0.15,       # 间隔分析策略
            'trend_reverse': 0.10,      # 趋势反转策略
            'random_diversity': 0.15    # 随机多样性策略
        }
        
        # 性能跟踪
        self.performance_tracker = {
            'total_predictions': 0,
            'successful_kills': 0,
            'diversity_score': 0.0,
            'strategy_performance': {strategy: {'hits': 0, 'total': 0} 
                                   for strategy in self.strategy_weights.keys()}
        }
    
    def generate_red_kill_numbers(self, data: pd.DataFrame, period_num: int, 
                                 kill_count: int = 4) -> List[int]:
        """
        生成红球杀号 - 增强版多样性策略（优化为4个）
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量 (优化为4个)
            
        Returns:
            List[int]: 红球杀号列表
        """
        if len(data) < 5:
            return self._get_fallback_red_kills(kill_count)
        
        # 生成多样性种子
        diversity_seed = self._generate_diversity_seed(period_num)
        
        # 获取分析数据
        recent_data = data.tail(20)  # 扩大分析窗口
        historical_numbers = self._extract_red_numbers(recent_data)
        
        # 多策略生成候选杀号
        strategy_candidates = {}
        
        # 策略1: 反频率策略 - 杀掉高频号码
        strategy_candidates['frequency_inverse'] = self._frequency_inverse_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 策略2: 模式打破策略 - 打破连续模式
        strategy_candidates['pattern_break'] = self._pattern_break_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 策略3: 区间平衡策略 - 平衡各区间
        strategy_candidates['zone_balance'] = self._zone_balance_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 策略4: 间隔分析策略 - 分析号码间隔
        strategy_candidates['gap_analysis'] = self._gap_analysis_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 策略5: 趋势反转策略 - 反转当前趋势
        strategy_candidates['trend_reverse'] = self._trend_reverse_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 策略6: 随机多样性策略 - 增加随机性
        strategy_candidates['random_diversity'] = self._random_diversity_strategy(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 智能融合策略结果
        final_kills = self._intelligent_merge_strategies(
            strategy_candidates, kill_count, 'red', diversity_seed
        )
        
        # 多样性检查和调整
        final_kills = self._ensure_diversity(final_kills, 'red', kill_count, diversity_seed)
        
        # 更新历史记录
        self._update_kill_history(period_num, final_kills, [])
        
        print(f"[增强杀号] 期号{period_num}: 红球{final_kills}")
        print(f"[多样性] 得分: {self._calculate_diversity_score(final_kills, []):.2f}")
        
        return sorted(final_kills[:kill_count])
    
    def generate_blue_kill_numbers(self, data: pd.DataFrame, period_num: int,
                                  kill_count: int = 1) -> List[int]:
        """
        增强版蓝球多样化杀号策略（优化为1个）
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量 (优化为1个)
            
        Returns:
            List[int]: 蓝球杀号列表
        """
        if len(data) < 2:
            return self._get_fallback_blue_kills(kill_count)
        
        # 生成多样性种子
        diversity_seed = self._generate_diversity_seed(period_num)
        
        # 获取分析数据
        recent_data = data.tail(15)  # 蓝球分析窗口
        historical_numbers = self._extract_blue_numbers(recent_data)
        
        # 蓝球专用策略
        strategy_candidates = {}
        
        # 频率反转策略
        strategy_candidates['frequency_inverse'] = self._blue_frequency_inverse(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 奇偶平衡策略
        strategy_candidates['parity_balance'] = self._blue_parity_balance(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 区间轮换策略
        strategy_candidates['zone_rotation'] = self._blue_zone_rotation(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 随机多样性
        strategy_candidates['random_diversity'] = self._blue_random_diversity(
            historical_numbers, kill_count, diversity_seed
        )
        
        # 融合策略
        final_kills = self._intelligent_merge_strategies(
            strategy_candidates, kill_count, 'blue', diversity_seed
        )
        
        # 多样性检查
        final_kills = self._ensure_diversity(final_kills, 'blue', kill_count, diversity_seed)
        
        # 更新历史记录
        self._update_kill_history(period_num, [], final_kills)
        
        print(f"[增强杀号] 期号{period_num}: 蓝球{final_kills}")
        
        return sorted(final_kills[:kill_count])
    
    def _generate_diversity_seed(self, period_num: int) -> int:
        """生成多样性种子 - 确保每期杀号不同"""
        import time
        import random
        
        # 组合多个随机源
        time_component = int(time.time() * 1000) % 10000
        period_component = int(period_num) % 1000
        random_component = random.randint(1, 1000)
        
        # 历史影响因子
        history_factor = len(self.kill_history['red']) * 100
        
        diversity_seed = (
            time_component + 
            period_component * 10 + 
            random_component + 
            history_factor
        ) % 100000
        
        return diversity_seed
    
    def _frequency_inverse_strategy(self, historical_numbers: List[List[int]], 
                                  count: int, seed: int) -> List[int]:
        """反频率策略 - 杀掉高频号码"""
        import random
        
        # 统计频率
        all_numbers = []
        for period in historical_numbers[-10:]:  # 最近10期
            all_numbers.extend(period)
        
        freq_counter = Counter(all_numbers)
        
        # 选择高频号码作为杀号候选
        high_freq_numbers = [num for num, freq in freq_counter.most_common(15)]
        
        # 添加随机性
        random.seed(seed)
        random.shuffle(high_freq_numbers)
        
        return high_freq_numbers[:count * 2]  # 返回更多候选
    
    def _pattern_break_strategy(self, historical_numbers: List[List[int]], 
                              count: int, seed: int) -> List[int]:
        """模式打破策略 - 打破连续和规律模式"""
        import random
        
        if len(historical_numbers) < 3:
            return []
        
        # 分析连续号码模式
        recent_period = historical_numbers[-1]
        consecutive_candidates = []
        
        sorted_numbers = sorted(recent_period)
        for i in range(len(sorted_numbers) - 1):
            if sorted_numbers[i+1] - sorted_numbers[i] <= 2:
                # 杀掉可能的连续号码
                next_consecutive = sorted_numbers[i+1] + 1
                if next_consecutive <= 35:
                    consecutive_candidates.append(next_consecutive)
                prev_consecutive = sorted_numbers[i] - 1
                if prev_consecutive >= 1:
                    consecutive_candidates.append(prev_consecutive)
        
        # 添加随机性
        random.seed(seed + 100)
        random.shuffle(consecutive_candidates)
        
        return consecutive_candidates[:count * 2]
    
    def _zone_balance_strategy(self, historical_numbers: List[List[int]], 
                             count: int, seed: int) -> List[int]:
        """区间平衡策略 - 平衡各区间出号"""
        import random
        
        # 定义区间
        zones = {
            'zone1': list(range(1, 8)),    # 1-7
            'zone2': list(range(8, 15)),   # 8-14
            'zone3': list(range(15, 22)),  # 15-21
            'zone4': list(range(22, 29)),  # 22-28
            'zone5': list(range(29, 36))   # 29-35
        }
        
        # 统计各区间频率
        zone_counts = {zone: 0 for zone in zones}
        
        for period in historical_numbers[-8:]:  # 最近8期
            for num in period:
                for zone_name, zone_range in zones.items():
                    if num in zone_range:
                        zone_counts[zone_name] += 1
                        break
        
        # 找出最活跃的区间，杀掉部分号码
        most_active_zone = max(zone_counts, key=zone_counts.get)
        zone_candidates = zones[most_active_zone].copy()
        
        # 添加随机性
        random.seed(seed + 200)
        random.shuffle(zone_candidates)
        
        return zone_candidates[:count * 2]
    
    def _gap_analysis_strategy(self, historical_numbers: List[List[int]], 
                             count: int, seed: int) -> List[int]:
        """间隔分析策略"""
        import random
        
        if len(historical_numbers) < 2:
            return []
        
        # 分析最近期的间隔
        recent_period = sorted(historical_numbers[-1])
        gaps = []
        
        for i in range(len(recent_period) - 1):
            gap = recent_period[i+1] - recent_period[i]
            gaps.append(gap)
        
        # 预测可能的间隔模式，杀掉相关号码
        gap_candidates = []
        
        # 如果间隔过小，杀掉连续号码
        if any(gap <= 2 for gap in gaps):
            for num in recent_period:
                if num + 1 <= 35:
                    gap_candidates.append(num + 1)
                if num - 1 >= 1:
                    gap_candidates.append(num - 1)
        
        # 如果间隔过大，杀掉中间号码
        for i, gap in enumerate(gaps):
            if gap > 6:
                start = recent_period[i]
                end = recent_period[i+1]
                middle_range = list(range(start + 2, end - 1))
                gap_candidates.extend(middle_range)
        
        # 添加随机性
        random.seed(seed + 300)
        random.shuffle(gap_candidates)
        
        return gap_candidates[:count * 2]
    
    def _trend_reverse_strategy(self, historical_numbers: List[List[int]], 
                              count: int, seed: int) -> List[int]:
        """趋势反转策略"""
        import random
        
        if len(historical_numbers) < 5:
            return []
        
        # 分析最近5期的趋势
        recent_5_periods = historical_numbers[-5:]
        
        # 统计奇偶趋势
        odd_counts = []
        for period in recent_5_periods:
            odd_count = sum(1 for num in period if num % 2 == 1)
            odd_counts.append(odd_count)
        
        avg_odd = sum(odd_counts) / len(odd_counts)
        
        # 反转趋势
        trend_candidates = []
        if avg_odd > 3:  # 奇数过多，杀奇数
            trend_candidates = [num for num in range(1, 36) if num % 2 == 1]
        elif avg_odd < 2:  # 偶数过多，杀偶数
            trend_candidates = [num for num in range(1, 36) if num % 2 == 0]
        
        # 添加随机性
        random.seed(seed + 400)
        random.shuffle(trend_candidates)
        
        return trend_candidates[:count * 2]
    
    def _random_diversity_strategy(self, historical_numbers: List[List[int]], 
                                 count: int, seed: int) -> List[int]:
        """随机多样性策略"""
        import random
        
        # 收集最近出现的号码
        recent_numbers = set()
        for period in historical_numbers[-5:]:
            recent_numbers.update(period)
        
        # 从未出现的号码中选择
        all_numbers = set(range(1, 36))
        unused_numbers = list(all_numbers - recent_numbers)
        
        # 添加随机性
        random.seed(seed + 500)
        random.shuffle(unused_numbers)
        
        if len(unused_numbers) >= count * 2:
            return unused_numbers[:count * 2]
        else:
            # 补充一些低频号码
            all_nums = list(all_numbers)
            random.shuffle(all_nums)
            return all_nums[:count * 2]
    
    def _blue_frequency_inverse(self, historical_numbers: List[List[int]], 
                              count: int, seed: int) -> List[int]:
        """蓝球反频率策略"""
        import random
        
        # 统计蓝球频率
        all_blues = []
        for period in historical_numbers[-8:]:
            all_blues.extend(period)
        
        freq_counter = Counter(all_blues)
        
        # 选择高频蓝球
        high_freq_blues = [num for num, freq in freq_counter.most_common(6)]
        
        # 添加随机性
        random.seed(seed + 600)
        random.shuffle(high_freq_blues)
        
        return high_freq_blues[:count * 3]
    
    def _blue_parity_balance(self, historical_numbers: List[List[int]], 
                           count: int, seed: int) -> List[int]:
        """蓝球奇偶平衡策略"""
        import random
        
        # 统计奇偶分布
        odd_count = 0
        even_count = 0
        
        for period in historical_numbers[-5:]:
            for num in period:
                if num % 2 == 0:
                    even_count += 1
                else:
                    odd_count += 1
        
        # 平衡策略
        parity_candidates = []
        if odd_count > even_count * 1.5:
            # 杀奇数
            parity_candidates = [i for i in range(1, 13) if i % 2 == 1]
        elif even_count > odd_count * 1.5:
            # 杀偶数
            parity_candidates = [i for i in range(1, 13) if i % 2 == 0]
        else:
            # 随机选择
            parity_candidates = list(range(1, 13))
        
        # 添加随机性
        random.seed(seed + 700)
        random.shuffle(parity_candidates)
        
        return parity_candidates[:count * 3]
    
    def _blue_zone_rotation(self, historical_numbers: List[List[int]], 
                          count: int, seed: int) -> List[int]:
        """蓝球区间轮换策略"""
        import random
        
        # 蓝球区间: 1-3, 4-6, 7-9, 10-12
        zones = {
            'zone1': [1, 2, 3],
            'zone2': [4, 5, 6],
            'zone3': [7, 8, 9],
            'zone4': [10, 11, 12]
        }
        
        # 统计各区间频率
        zone_counts = {zone: 0 for zone in zones}
        
        for period in historical_numbers[-6:]:
            for num in period:
                for zone_name, zone_range in zones.items():
                    if num in zone_range:
                        zone_counts[zone_name] += 1
                        break
        
        # 选择最活跃区间
        most_active_zone = max(zone_counts, key=zone_counts.get)
        zone_candidates = zones[most_active_zone].copy()
        
        # 添加随机性
        random.seed(seed + 800)
        random.shuffle(zone_candidates)
        
        return zone_candidates[:count * 3]
    
    def _blue_random_diversity(self, historical_numbers: List[List[int]], 
                             count: int, seed: int) -> List[int]:
        """蓝球随机多样性策略"""
        import random
        
        # 收集最近出现的蓝球
        recent_blues = set()
        for period in historical_numbers[-4:]:
            recent_blues.update(period)
        
        # 从未出现的蓝球中选择
        all_blues = set(range(1, 13))
        unused_blues = list(all_blues - recent_blues)
        
        # 添加随机性
        random.seed(seed + 900)
        random.shuffle(unused_blues)
        
        if len(unused_blues) >= count * 3:
            return unused_blues[:count * 3]
        else:
            all_blues_list = list(all_blues)
            random.shuffle(all_blues_list)
            return all_blues_list[:count * 3]
    
    def _intelligent_merge_strategies(self, strategy_candidates: Dict[str, List[int]], 
                                    count: int, ball_type: str, seed: int) -> List[int]:
        """智能融合多策略结果"""
        import random
        
        # 按权重收集候选
        weighted_candidates = []
        
        for strategy, candidates in strategy_candidates.items():
            weight = self.strategy_weights.get(strategy, 0.1)
            for candidate in candidates:
                weighted_candidates.append((candidate, weight))
        
        # 计算候选分数
        candidate_scores = defaultdict(float)
        for candidate, weight in weighted_candidates:
            candidate_scores[candidate] += weight
        
        # 排序并选择
        sorted_candidates = sorted(candidate_scores.items(), 
                                 key=lambda x: x[1], reverse=True)
        
        # 添加随机性避免固定模式
        random.seed(seed + 1000)
        top_candidates = [num for num, _ in sorted_candidates[:count * 3]]
        random.shuffle(top_candidates)
        
        return top_candidates[:count * 2]
    
    def _ensure_diversity(self, candidates: List[int], ball_type: str, 
                         count: int, seed: int) -> List[int]:
        """确保杀号多样性"""
        import random
        
        # 检查与历史杀号的重复度
        history_key = ball_type
        recent_kills = self.kill_history[history_key][-5:]  # 最近5期
        
        # 计算重复度
        all_recent_kills = set()
        for kill_record in recent_kills:
            all_recent_kills.update(kill_record)
        
        # 过滤重复号码
        diverse_candidates = []
        for candidate in candidates:
            if candidate not in all_recent_kills:
                diverse_candidates.append(candidate)
        
        # 如果多样性候选不够，添加一些随机号码
        if len(diverse_candidates) < count:
            ball_range = self.red_range if ball_type == 'red' else self.blue_range
            remaining_numbers = [num for num in ball_range 
                               if num not in diverse_candidates and num not in all_recent_kills]
            
            random.seed(seed + 2000)
            random.shuffle(remaining_numbers)
            diverse_candidates.extend(remaining_numbers[:count - len(diverse_candidates)])
        
        return diverse_candidates[:count]
    
    def _extract_red_numbers(self, data: pd.DataFrame) -> List[List[int]]:
        """提取红球号码"""
        red_numbers = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            if len(red_balls) == 5:
                red_numbers.append(red_balls)
        return red_numbers
    
    def _extract_blue_numbers(self, data: pd.DataFrame) -> List[List[int]]:
        """提取蓝球号码"""
        blue_numbers = []
        for _, row in data.iterrows():
            _, blue_balls = parse_numbers(row)
            if len(blue_balls) == 2:
                blue_numbers.append(blue_balls)
        return blue_numbers
    
    def _update_kill_history(self, period_num: int, red_kills: List[int], 
                           blue_kills: List[int]):
        """更新杀号历史"""
        # 添加到历史记录
        if red_kills:
            self.kill_history['red'].append(red_kills)
        if blue_kills:
            self.kill_history['blue'].append(blue_kills)
        
        # 保持历史窗口大小
        if len(self.kill_history['red']) > self.history_window:
            self.kill_history['red'].pop(0)
        if len(self.kill_history['blue']) > self.history_window:
            self.kill_history['blue'].pop(0)
    
    def _calculate_diversity_score(self, red_kills: List[int], 
                                 blue_kills: List[int]) -> float:
        """计算多样性得分"""
        # 检查与最近杀号的差异度
        red_history = self.kill_history['red'][-3:]  # 最近3期
        blue_history = self.kill_history['blue'][-3:]
        
        red_diversity = 1.0
        blue_diversity = 1.0
        
        if red_history and red_kills:
            red_overlap = 0
            for hist_kills in red_history:
                overlap = len(set(red_kills) & set(hist_kills))
                red_overlap += overlap
            red_diversity = max(0.0, 1.0 - red_overlap / (len(red_history) * len(red_kills)))
        
        if blue_history and blue_kills:
            blue_overlap = 0
            for hist_kills in blue_history:
                overlap = len(set(blue_kills) & set(hist_kills))
                blue_overlap += overlap
            blue_diversity = max(0.0, 1.0 - blue_overlap / (len(blue_history) * len(blue_kills)))
        
        return (red_diversity + blue_diversity) / 2.0
    
    def _get_fallback_red_kills(self, count: int) -> List[int]:
        """数据不足时的备用杀号策略"""
        # 使用经验杀号: 通常较少出现的号码
        fallback_numbers = [1, 2, 34, 35, 7, 14, 21, 28, 3, 6, 9, 12, 15, 18]
        return fallback_numbers[:count]
    
    def _get_fallback_blue_kills(self, count: int) -> List[int]:
        """蓝球数据不足时的备用杀号策略"""
        # 使用经验杀号: 蓝球中较少出现的号码
        fallback_blues = [1, 2, 12, 11, 3, 10]
        return fallback_blues[:count]
    
    def update_performance(self, period_result: List[int], blue_result: List[int]):
        """更新性能统计"""
        if not self.kill_history['red'] or not self.kill_history['blue']:
            return
        
        # 获取最近的杀号
        last_red_kills = self.kill_history['red'][-1] if self.kill_history['red'] else []
        last_blue_kills = self.kill_history['blue'][-1] if self.kill_history['blue'] else []
        
        # 检查杀号成功率
        red_success = not any(num in period_result for num in last_red_kills) if last_red_kills else True
        blue_success = not any(num in blue_result for num in last_blue_kills) if last_blue_kills else True
        
        if red_success and blue_success:
            self.performance_tracker['successful_kills'] += 1
        
        # 更新多样性得分
        diversity_score = self._calculate_diversity_score(last_red_kills, last_blue_kills)
        self.performance_tracker['diversity_score'] = (
            self.performance_tracker['diversity_score'] * 0.9 + diversity_score * 0.1
        )
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total = self.performance_tracker['total_predictions']
        success = self.performance_tracker['successful_kills']
        
        return {
            'total_predictions': total,
            'successful_kills': success,
            'success_rate': success / total if total > 0 else 0.0,
            'diversity_score': self.performance_tracker['diversity_score'],
            'strategy_weights': self.strategy_weights.copy()
        }


# 向后兼容的类别名
NumberKiller = UnifiedKillAlgorithms
UniversalKiller = UnifiedKillAlgorithms
BlueKiller = UnifiedKillAlgorithms
