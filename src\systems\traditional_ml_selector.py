#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Traditional ML Selector - 传统机器学习选号器
使用随机森林、贝叶斯、马尔可夫链等传统机器学习方法进行选号
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
import time
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关导入
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, GradientBoostingRegressor
from sklearn.ensemble import ExtraTreesRegressor, AdaBoostRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib

# 导入现有组件
from ..models.bayes.bayes_selector import BayesSelector
from ..models.markov.markov_model import MarkovModel


class TraditionalMLSelector:
    """传统机器学习选号器"""
    
    def __init__(self, config):
        """
        初始化传统机器学习选号器
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 模型存储
        self.models = {}
        self.model_scores = {}
        
        # 模型配置
        self.model_configs = {
            'random_forest': {
                'n_estimators': self.config.ml_n_estimators,
                'max_depth': self.config.ml_max_depth,
                'random_state': self.config.ml_random_state,
                'n_jobs': -1
            },
            'gradient_boosting': {
                'n_estimators': self.config.ml_n_estimators,
                'max_depth': self.config.ml_max_depth,
                'random_state': self.config.ml_random_state
            },
            'extra_trees': {
                'n_estimators': self.config.ml_n_estimators,
                'max_depth': self.config.ml_max_depth,
                'random_state': self.config.ml_random_state,
                'n_jobs': -1
            }
        }
        
        # 贝叶斯和马尔可夫组件
        self.bayes_selector = None
        self.markov_model = None
        
        # 模型保存路径
        self.models_dir = Path("models/traditional_ml_selection")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("传统机器学习选号器初始化完成")
    
    def train_and_select(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        训练模型并进行选号
        
        Args:
            processed_data: 处理后的数据
            
        Returns:
            Dict: 选号结果
        """
        self.logger.info("开始传统机器学习模型训练和选号...")
        
        # 训练多个模型
        models_results = {}
        
        # 1. 随机森林模型
        self.logger.info("训练随机森林模型...")
        rf_result = self._train_random_forest(processed_data)
        models_results['random_forest'] = rf_result
        
        # 2. 梯度提升模型
        self.logger.info("训练梯度提升模型...")
        gb_result = self._train_gradient_boosting(processed_data)
        models_results['gradient_boosting'] = gb_result
        
        # 3. 极端随机树模型
        self.logger.info("训练极端随机树模型...")
        et_result = self._train_extra_trees(processed_data)
        models_results['extra_trees'] = et_result
        
        # 4. 贝叶斯选号器
        self.logger.info("训练贝叶斯选号器...")
        bayes_result = self._train_bayes_selector(processed_data)
        models_results['bayes'] = bayes_result
        
        # 5. 马尔可夫链模型
        self.logger.info("训练马尔可夫链模型...")
        markov_result = self._train_markov_model(processed_data)
        models_results['markov'] = markov_result
        
        # 集成结果
        ensemble_result = self._ensemble_models(models_results, processed_data)
        
        self.logger.info("传统机器学习模型训练和选号完成")
        return ensemble_result
    
    def select_numbers(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用已训练模型进行选号
        
        Args:
            processed_data: 处理后的数据
            
        Returns:
            Dict: 选号结果
        """
        if not self.models:
            return self.train_and_select(processed_data)
        
        # 使用最新数据进行预测
        latest_features = processed_data['test_data']['features_scaled'].iloc[-1:] if len(processed_data['test_data']['features_scaled']) > 0 else None
        
        if latest_features is None:
            return self._fallback_selection()
        
        predictions = {}
        
        for model_name, model in self.models.items():
            try:
                if hasattr(model, 'predict'):
                    pred = model.predict(latest_features)
                    predictions[model_name] = pred
            except Exception as e:
                self.logger.warning(f"模型 {model_name} 预测失败: {e}")
        
        # 集成预测结果
        return self._ensemble_predictions(predictions)
    
    def _train_random_forest(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练随机森林模型"""
        try:
            # 获取训练数据
            train_X = processed_data['train_data']['features_scaled']
            train_y_red = processed_data['train_data']['targets']['red_balls']
            train_y_blue = processed_data['train_data']['targets']['blue_balls']
            
            val_X = processed_data['val_data']['features_scaled']
            val_y_red = processed_data['val_data']['targets']['red_balls']
            val_y_blue = processed_data['val_data']['targets']['blue_balls']
            
            # 训练红球模型
            rf_red = MultiOutputRegressor(
                RandomForestRegressor(**self.model_configs['random_forest'])
            )
            rf_red.fit(train_X, train_y_red)
            
            # 训练蓝球模型
            rf_blue = MultiOutputRegressor(
                RandomForestRegressor(**self.model_configs['random_forest'])
            )
            rf_blue.fit(train_X, train_y_blue)
            
            # 验证模型
            val_pred_red = rf_red.predict(val_X)
            val_pred_blue = rf_blue.predict(val_X)
            
            red_score = mean_squared_error(val_y_red, val_pred_red)
            blue_score = mean_squared_error(val_y_blue, val_pred_blue)
            
            # 保存模型
            self.models['rf_red'] = rf_red
            self.models['rf_blue'] = rf_blue
            self.model_scores['random_forest'] = {'red_mse': red_score, 'blue_mse': blue_score}
            
            # 保存到文件
            if self.config.save_models:
                joblib.dump(rf_red, self.models_dir / 'rf_red.pkl')
                joblib.dump(rf_blue, self.models_dir / 'rf_blue.pkl')
            
            return {
                'red_model': rf_red,
                'blue_model': rf_blue,
                'red_score': red_score,
                'blue_score': blue_score,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"随机森林模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _train_gradient_boosting(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练梯度提升模型"""
        try:
            # 获取训练数据
            train_X = processed_data['train_data']['features_scaled']
            train_y_red = processed_data['train_data']['targets']['red_balls']
            train_y_blue = processed_data['train_data']['targets']['blue_balls']
            
            val_X = processed_data['val_data']['features_scaled']
            val_y_red = processed_data['val_data']['targets']['red_balls']
            val_y_blue = processed_data['val_data']['targets']['blue_balls']
            
            # 训练红球模型
            gb_red = MultiOutputRegressor(
                GradientBoostingRegressor(**self.model_configs['gradient_boosting'])
            )
            gb_red.fit(train_X, train_y_red)
            
            # 训练蓝球模型
            gb_blue = MultiOutputRegressor(
                GradientBoostingRegressor(**self.model_configs['gradient_boosting'])
            )
            gb_blue.fit(train_X, train_y_blue)
            
            # 验证模型
            val_pred_red = gb_red.predict(val_X)
            val_pred_blue = gb_blue.predict(val_X)
            
            red_score = mean_squared_error(val_y_red, val_pred_red)
            blue_score = mean_squared_error(val_y_blue, val_pred_blue)
            
            # 保存模型
            self.models['gb_red'] = gb_red
            self.models['gb_blue'] = gb_blue
            self.model_scores['gradient_boosting'] = {'red_mse': red_score, 'blue_mse': blue_score}
            
            return {
                'red_model': gb_red,
                'blue_model': gb_blue,
                'red_score': red_score,
                'blue_score': blue_score,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"梯度提升模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _train_extra_trees(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练极端随机树模型"""
        try:
            # 获取训练数据
            train_X = processed_data['train_data']['features_scaled']
            train_y_red = processed_data['train_data']['targets']['red_balls']
            train_y_blue = processed_data['train_data']['targets']['blue_balls']
            
            # 训练模型
            et_red = MultiOutputRegressor(
                ExtraTreesRegressor(**self.model_configs['extra_trees'])
            )
            et_red.fit(train_X, train_y_red)
            
            et_blue = MultiOutputRegressor(
                ExtraTreesRegressor(**self.model_configs['extra_trees'])
            )
            et_blue.fit(train_X, train_y_blue)
            
            # 保存模型
            self.models['et_red'] = et_red
            self.models['et_blue'] = et_blue
            
            return {
                'red_model': et_red,
                'blue_model': et_blue,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"极端随机树模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _train_bayes_selector(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练贝叶斯选号器"""
        try:
            # 初始化贝叶斯选择器
            self.bayes_selector = BayesSelector('red')
            
            # 使用历史数据设置先验概率
            raw_data = processed_data['raw_data']
            
            # 计算先验概率
            red_cols = ['红球1', '红球2', '红球3', '红球4', '红球5']
            all_red_numbers = []
            for col in red_cols:
                all_red_numbers.extend(raw_data[col].tolist())
            
            # 计算每个号码的出现频率作为先验概率
            from collections import Counter
            red_counts = Counter(all_red_numbers)
            total_count = sum(red_counts.values())
            
            prior_probs = {}
            for num in range(1, 36):
                prior_probs[str(num)] = red_counts.get(num, 0) / total_count
            
            self.bayes_selector.set_prior_probabilities(prior_probs)
            
            return {
                'bayes_selector': self.bayes_selector,
                'prior_probs': prior_probs,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"贝叶斯选号器训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _train_markov_model(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练马尔可夫链模型"""
        try:
            # 初始化马尔可夫模型
            self.markov_model = MarkovModel('red', order=2)
            
            # 使用历史数据训练
            raw_data = processed_data['raw_data']
            
            # 构建状态序列
            red_cols = ['红球1', '红球2', '红球3', '红球4', '红球5']
            
            # 简化状态表示（使用奇偶和大小组合）
            states = []
            for _, row in raw_data.iterrows():
                red_numbers = [row[col] for col in red_cols]
                
                # 计算奇偶比和大小比
                odd_count = sum(1 for num in red_numbers if num % 2 == 1)
                large_count = sum(1 for num in red_numbers if num > 18)
                
                state = f"odd_{odd_count}_large_{large_count}"
                states.append(state)
            
            # 训练马尔可夫链（简化版本）
            self.markov_model.states = list(set(states))
            self.markov_model.is_trained = True
            
            return {
                'markov_model': self.markov_model,
                'states': states,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"马尔可夫链模型训练失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _ensemble_models(self, models_results: Dict[str, Dict], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """集成多个模型的结果"""
        successful_models = {k: v for k, v in models_results.items() if v.get('success', False)}
        
        if not successful_models:
            return self._fallback_selection()
        
        # 使用最新数据进行预测
        latest_features = processed_data['test_data']['features_scaled'].iloc[-1:] if len(processed_data['test_data']['features_scaled']) > 0 else None
        
        if latest_features is None:
            return self._fallback_selection()
        
        # 收集所有模型的预测
        all_predictions = {}
        
        # 树模型预测
        for model_type in ['random_forest', 'gradient_boosting', 'extra_trees']:
            if model_type in successful_models:
                try:
                    red_model_key = f"{model_type.split('_')[0]}_red" if model_type == 'random_forest' else f"{model_type.split('_')[0]}{model_type.split('_')[1][0] if len(model_type.split('_')) > 1 else ''}_red"
                    blue_model_key = f"{model_type.split('_')[0]}_blue" if model_type == 'random_forest' else f"{model_type.split('_')[0]}{model_type.split('_')[1][0] if len(model_type.split('_')) > 1 else ''}_blue"
                    
                    if red_model_key in self.models and blue_model_key in self.models:
                        red_pred = self.models[red_model_key].predict(latest_features)
                        blue_pred = self.models[blue_model_key].predict(latest_features)
                        all_predictions[model_type] = (red_pred, blue_pred)
                except Exception as e:
                    self.logger.warning(f"模型 {model_type} 预测失败: {e}")
        
        # 贝叶斯预测
        if 'bayes' in successful_models and self.bayes_selector:
            try:
                # 简化的贝叶斯预测
                bayes_red = self._bayes_predict_numbers()
                bayes_blue = np.random.choice(range(1, 13), 2, replace=False)
                all_predictions['bayes'] = (bayes_red.reshape(1, -1), bayes_blue.reshape(1, -1))
            except Exception as e:
                self.logger.warning(f"贝叶斯预测失败: {e}")
        
        # 集成预测结果
        if all_predictions:
            return self._ensemble_predictions(all_predictions)
        else:
            return self._fallback_selection()
    
    def _bayes_predict_numbers(self) -> np.ndarray:
        """使用贝叶斯方法预测号码"""
        if not self.bayes_selector or not self.bayes_selector.is_initialized:
            return np.random.choice(range(1, 36), 5, replace=False)
        
        # 获取先验概率
        probs = self.bayes_selector.prior_probabilities
        
        # 转换为数值概率
        numbers = []
        probabilities = []
        
        for num_str, prob in probs.items():
            try:
                num = int(num_str)
                if 1 <= num <= 35:
                    numbers.append(num)
                    probabilities.append(prob)
            except:
                continue
        
        if len(numbers) < 5:
            return np.random.choice(range(1, 36), 5, replace=False)
        
        # 根据概率选择号码
        probabilities = np.array(probabilities)
        probabilities = probabilities / probabilities.sum()  # 归一化
        
        selected = np.random.choice(numbers, 5, replace=False, p=probabilities)
        return selected
    
    def _ensemble_predictions(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """集成预测结果"""
        if not predictions:
            return self._fallback_selection()
        
        # 收集所有红球和蓝球预测
        red_preds = []
        blue_preds = []
        
        for model_name, pred in predictions.items():
            if isinstance(pred, (tuple, list)) and len(pred) >= 2:
                red_preds.append(pred[0])
                blue_preds.append(pred[1])
        
        if red_preds and blue_preds:
            # 平均预测值
            avg_red = np.mean(red_preds, axis=0).flatten()
            avg_blue = np.mean(blue_preds, axis=0).flatten()
            
            # 转换为整数号码
            red_balls = [max(1, min(35, int(round(x)))) for x in avg_red]
            blue_balls = [max(1, min(12, int(round(x)))) for x in avg_blue]
            
            # 确保号码唯一性
            red_balls = list(set(red_balls))
            while len(red_balls) < 5:
                new_num = np.random.randint(1, 36)
                if new_num not in red_balls:
                    red_balls.append(new_num)
            red_balls = sorted(red_balls[:5])
            
            blue_balls = list(set(blue_balls))
            while len(blue_balls) < 2:
                new_num = np.random.randint(1, 13)
                if new_num not in blue_balls:
                    blue_balls.append(new_num)
            blue_balls = sorted(blue_balls[:2])
            
            confidence = min(1.0, len(predictions) / 5.0)  # 基于成功模型数量的置信度
            
            return {
                'red_balls': red_balls,
                'blue_balls': blue_balls,
                'confidence': confidence,
                'model_info': {
                    'method': 'traditional_ml_ensemble',
                    'models_used': list(predictions.keys()),
                    'ensemble_type': 'average'
                }
            }
        
        return self._fallback_selection()
    
    def _fallback_selection(self) -> Dict[str, Any]:
        """后备选号方法"""
        self.logger.warning("使用后备选号方法")
        
        # 简单随机选号
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        return {
            'red_balls': red_balls.tolist(),
            'blue_balls': blue_balls.tolist(),
            'confidence': 0.1,
            'model_info': {
                'method': 'random_fallback',
                'reason': 'traditional_ml_failed'
            }
        }
